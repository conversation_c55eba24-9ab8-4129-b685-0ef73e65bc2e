#!/usr/bin/env python3
"""
WOLVE VIP - Binary Options Trading Indicator
Version: 2.8
Contact: @Wolvestrading1
Telegram: https://t.me/MRTrader10M

Complete standalone application for binary options trading signals.
"""

import datetime
import os
import random

# ============================================================================
# LICENSE CONFIGURATION
# ============================================================================
# Set the expiration date for the license (YEAR, MONTH, DAY)
# The bot will stop working after this date
LICENSE_EXPIRATION_DATE = datetime.date(2025, 12, 31)  # Change this date as needed

# ============================================================================

# Try to import colorama for colors
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    # Install colorama if not available
    os.system("pip install colorama")
    try:
        from colorama import init, Fore, Style
        init(autoreset=True)
        COLORAMA_AVAILABLE = True
    except ImportError:
        # Fallback if colorama fails
        class Fore:
            CYAN = LIGHTCYAN_EX = BLUE = LIGHTBLUE_EX = ""
            RED = GREEN = YELLOW = MAGENTA = WHITE = ""
        class Style:
            RESET_ALL = ""
        COLORAMA_AVAILABLE = False

def generate_banner():
    """Generate the WOLVE VIP banner with sky blue colors"""
    banner = f"""{Fore.LIGHTCYAN_EX}
██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

        ██╗    ██╗ ██████╗ ██╗    ██╗   ██╗███████╗    ██╗   ██╗██╗██████╗
        ██║    ██║██╔═══██╗██║    ██║   ██║██╔════╝    ██║   ██║██║██╔══██╗
        ██║ █╗ ██║██║   ██║██║    ██║   ██║█████╗      ██║   ██║██║██████╔╝
        ██║███╗██║██║   ██║██║    ╚██╗ ██╔╝██╔══╝      ╚██╗ ██╔╝██║██╔═══╝
        ╚███╔███╔╝╚██████╔╝███████╗╚████╔╝ ███████╗     ╚████╔╝ ██║██║
         ╚══╝╚══╝  ╚═════╝ ╚══════╝ ╚═══╝  ╚══════╝      ╚═══╝  ╚═╝╚═╝

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

                                        TELEGRAM: https://t.me/MRTrader10M
                                        CONTRACT: @Wolvestrading1
                                        VERSION: 2.8

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{Style.RESET_ALL}"""
    return banner

def check_license_expiration():
    """Check if the license has expired"""
    current_date = datetime.date.today()

    if current_date > LICENSE_EXPIRATION_DATE:
        return False, current_date
    return True, current_date

def check_license(email, password):
    """Check license validity with email, password, and expiration date"""

    # First check if license has expired
    print(f"{Fore.LIGHTCYAN_EX}Checking license expiration...")
    is_valid, current_date = check_license_expiration()

    if not is_valid:
        print(f"{Fore.RED}✗ LICENSE EXPIRED!")
        print(f"{Fore.RED}Current date: {current_date.strftime('%Y-%m-%d')}")
        print(f"{Fore.RED}License expired on: {LICENSE_EXPIRATION_DATE.strftime('%Y-%m-%d')}")
        print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license.")
        return False

    print(f"{Fore.GREEN}✓ License valid until: {LICENSE_EXPIRATION_DATE.strftime('%Y-%m-%d')}")

    # HARDCODED LOGIN CREDENTIALS
    valid_email = "<EMAIL>"
    valid_password = "12973"

    print(f"{Fore.LIGHTCYAN_EX}Checking credentials...")

    if email.lower() == valid_email.lower() and password == valid_password:
        print(f"{Fore.GREEN}✓ Login successful!")
        return True
    else:
        print(f"{Fore.RED}✗ Invalid email or password!")
        return False

def get_login_credentials():
    """Get email and password from user"""
    print(f"\n{Fore.LIGHTCYAN_EX}" + "="*60)
    print(f"{Fore.LIGHTCYAN_EX}                    LOGIN REQUIRED")
    print(f"{Fore.LIGHTCYAN_EX}" + "="*60)

    try:
        email = input(f"{Fore.LIGHTCYAN_EX}Email: ").strip()
        password = input(f"{Fore.LIGHTCYAN_EX}Password: ").strip()
        return email, password

    except KeyboardInterrupt:
        print(f"\n{Fore.RED}Login cancelled.")
        return None, None

# Removed old functions - using new INDICATOR GURU style interface

def get_timezone_selection():
    """Get timezone selection from user"""
    timezones = {
        1: ("Bangladesh", "UTC+6", "+06:00"),
        2: ("India", "UTC+5:30", "+05:30"),
        3: ("Pakistan", "UTC+5", "+05:00"),
        4: ("USA (Eastern)", "UTC-5/-4", "-05:00"),
        5: ("USA (Central)", "UTC-6/-5", "-06:00"),
        6: ("USA (Mountain)", "UTC-7/-6", "-07:00"),
        7: ("USA (Pacific)", "UTC-8/-7", "-08:00"),
        8: ("UK", "UTC+0/+1", "+00:00"),
        9: ("Dubai/UAE", "UTC+4", "+04:00"),
        10: ("Saudi Arabia", "UTC+3", "+03:00"),
        11: ("Malaysia", "UTC+8", "+08:00"),
        12: ("Nepal", "UTC+5:45", "+05:45"),
        13: ("Singapore", "UTC+8", "+08:00")
    }

    print(f"\n{Fore.LIGHTCYAN_EX}SELECT YOUR TIMEZONE:")
    print(f"{Fore.LIGHTCYAN_EX}" + "=" * 50)
    for num, (country, utc_info, offset) in timezones.items():
        print(f"{Fore.LIGHTCYAN_EX}{num:2d}. {country:<15} ({utc_info})")
    print(f"{Fore.LIGHTCYAN_EX}" + "=" * 50)

    while True:
        try:
            choice = int(input(f"{Fore.LIGHTCYAN_EX}SELECT TIMEZONE (1-13): "))
            if 1 <= choice <= 13:
                selected = timezones[choice]
                print(f"{Fore.GREEN}SELECTED: {selected[0]} ({selected[1]})")
                return selected
            else:
                print(f"{Fore.RED}Please enter a number between 1-13")
        except ValueError:
            print(f"{Fore.RED}Please enter a valid number")

def get_user_inputs():
    """Get all user inputs like original INDICATOR GURU"""

    # Currency pairs
    pairs_input = input(f"{Fore.LIGHTCYAN_EX}TYPE YOUR CURRENCY PAIRS (FORMAT: XXXYYY-OTC, SEPARATED BY COMMAS): ").strip().upper()
    pairs = [pair.strip() for pair in pairs_input.split(',')]

    # Martingale levels
    while True:
        try:
            martingale = int(input(f"{Fore.LIGHTCYAN_EX}HOW MANY MARTINGALE LEVELS DO YOU WANT (1 OR 2)? "))
            if martingale in [1, 2]:
                break
            else:
                print(f"{Fore.RED}Please enter 1 or 2")
        except ValueError:
            print(f"{Fore.RED}Please enter a valid number")

    # Broker
    while True:
        broker = input(f"{Fore.LIGHTCYAN_EX}BROKER NAME (QUOTEX OR POCKET OPTION): ").strip().upper()
        if broker in ['QUOTEX', 'POCKET OPTION']:
            break
        else:
            print(f"{Fore.RED}Please enter QUOTEX or POCKET OPTION")

    # Timezone selection
    timezone_info = get_timezone_selection()

    # Date
    while True:
        try:
            date_str = input(f"{Fore.LIGHTCYAN_EX}DATE SELECT (DD/MM/YYYY): ").strip()
            date_obj = datetime.datetime.strptime(date_str, "%d/%m/%Y")
            break
        except ValueError:
            print(f"{Fore.RED}Please enter date in DD/MM/YYYY format")

    # Day analysis
    while True:
        try:
            day_analysis = int(input(f"{Fore.LIGHTCYAN_EX}SELECT DAY ANALYSIS (1-7): "))
            if 1 <= day_analysis <= 7:
                break
            else:
                print(f"{Fore.RED}Please enter a number between 1-7")
        except ValueError:
            print(f"{Fore.RED}Please enter a valid number")

    # Signal direction
    while True:
        direction = input(f"{Fore.LIGHTCYAN_EX}SIGNAL DIRECTION (PUT, CALL, BOTH): ").strip().upper()
        if direction in ['PUT', 'CALL', 'BOTH']:
            break
        else:
            print(f"{Fore.RED}Please enter PUT, CALL, or BOTH")

    # Start time
    while True:
        try:
            start_time = input(f"{Fore.LIGHTCYAN_EX}START TIME (HH:MM): ").strip()
            datetime.datetime.strptime(start_time, "%H:%M")
            break
        except ValueError:
            print(f"{Fore.RED}Please enter time in HH:MM format")

    # End time
    while True:
        try:
            end_time = input(f"{Fore.LIGHTCYAN_EX}END TIME (HH:MM): ").strip()
            datetime.datetime.strptime(end_time, "%H:%M")
            break
        except ValueError:
            print(f"{Fore.RED}Please enter time in HH:MM format")

    return pairs, martingale, broker, timezone_info, date_str, day_analysis, direction, start_time, end_time

def generate_signals(pairs, direction, start_time, end_time, timezone_info=None):
    """Generate signals like original INDICATOR GURU"""

    print(f"{Fore.LIGHTCYAN_EX}Capturing News... complete!")
    print(f"{Fore.LIGHTCYAN_EX}Trend Filtering... complete!")
    print(f"{Fore.GREEN}SIGNAL GENERATE SUCCESSFUL!")

    # Show timezone info if provided
    if timezone_info:
        print(f"{Fore.LIGHTCYAN_EX}TIMEZONE: {timezone_info[0]} ({timezone_info[1]})")

    print(f"{Fore.LIGHTCYAN_EX}----- M1 -----")

    # Parse start and end times
    start_hour, start_min = map(int, start_time.split(':'))
    end_hour, end_min = map(int, end_time.split(':'))

    # Generate signals between start and end time
    signals = []
    current_hour = start_hour
    current_min = start_min

    while (current_hour < end_hour) or (current_hour == end_hour and current_min < end_min):
        # Random chance to generate signal (about 30% chance per minute)
        if random.random() < 0.3:
            # Select random pair
            pair = random.choice(pairs)

            # Select direction based on filter
            if direction == "BOTH":
                signal_direction = random.choice(["PUT", "CALL"])
            else:
                signal_direction = direction

            # Format signal in new format: "20:17 - NZDUSD - OTC PUT"
            time_str = f"{current_hour:02d}:{current_min:02d}"
            signal = f"{time_str} - {pair} - OTC {signal_direction}"
            signals.append(signal)

            # Color code the signals - CALL in green, PUT in red
            if signal_direction == "CALL":
                print(f"{Fore.GREEN}{signal}")
            else:  # PUT
                print(f"{Fore.RED}{signal}")

        # Increment time by 1-5 minutes randomly
        current_min += random.randint(1, 5)
        if current_min >= 60:
            current_hour += 1
            current_min = current_min % 60

    return signals

def main():
    """Main application function matching original INDICATOR GURU"""
    try:
        # Display banner
        print(generate_banner())

        # Check license expiration first (before login)
        is_valid, current_date = check_license_expiration()
        if not is_valid:
            print(f"\n{Fore.RED}{'='*60}")
            print(f"{Fore.RED}           LICENSE EXPIRED - BOT DISABLED")
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.RED}Current date: {current_date.strftime('%Y-%m-%d')}")
            print(f"{Fore.RED}License expired on: {LICENSE_EXPIRATION_DATE.strftime('%Y-%m-%d')}")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license.")
            print(f"{Fore.RED}{'='*60}")
            input(f"{Fore.LIGHTCYAN_EX}Press Enter to exit...")
            return

        # Get login credentials
        email, password = get_login_credentials()

        if not email or not password:
            print(f"{Fore.RED}Login cancelled!")
            return

        # Check credentials
        if not check_license(email, password):
            print(f"{Fore.RED}Access denied. Invalid credentials!")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 for assistance.")
            input(f"{Fore.LIGHTCYAN_EX}Press Enter to exit...")
            return

        # Main loop like original
        while True:
            # Get user inputs
            pairs, martingale, broker, timezone_info, date_str, day_analysis, direction, start_time, end_time = get_user_inputs()

            # Generate signals
            signals = generate_signals(pairs, direction, start_time, end_time, timezone_info)

            # Ask if user wants to continue
            while True:
                leave = input(f"{Fore.LIGHTCYAN_EX}DO YOU WANT TO LEAVE? (Y/N): ").strip().upper()
                if leave in ['Y', 'N']:
                    break
                else:
                    print(f"{Fore.RED}Please enter Y or N")

            if leave == 'Y':
                print(f"{Fore.GREEN}Thank you for using WOLVE VIP!")
                break

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Operation cancelled by user.")
    except Exception as e:
        print(f"\n{Fore.RED}An error occurred: {e}")
        print(f"{Fore.YELLOW}Please contact @Wolvestrading1 for support.")

if __name__ == "__main__":
    main()
